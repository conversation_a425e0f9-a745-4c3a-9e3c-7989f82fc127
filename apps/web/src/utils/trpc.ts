import { QueryCache, QueryClient } from '@tanstack/react-query';
import { httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import { toast } from 'sonner';

// Minimal type definition to avoid server imports during build
// This provides basic type safety for tRPC calls
interface AppRouter {
  healthCheck: {
    useQuery: () => any;
  };
  accounts: {
    getMonitored: {
      useQuery: () => any;
    };
    add: {
      useMutation: () => any;
    };
    toggleStatus: {
      useMutation: () => any;
    };
    remove: {
      useMutation: () => any;
    };
  };
  mentions: {
    getLatest: {
      useQuery: (params?: any) => any;
    };
    getAll: {
      useQuery: (params?: any) => any;
    };
    enhance: {
      useMutation: () => any;
    };
    syncAllAccounts: {
      useMutation: () => any;
    };
    syncAccount: {
      useMutation: () => any;
    };
    archive: {
      useMutation: () => any;
    };
    deleteResponse: {
      useMutation: () => any;
    };
  };
  benji: {
    generateMentionResponse: {
      useMutation: () => any;
    };
    generateQuickReply: {
      useMutation: () => any;
    };
  };
  twitter: {
    extractTweetContent: {
      useMutation: () => any;
    };
  };
  user: {
    getProfile: {
      useQuery: () => any;
    };
    getUsage: {
      useQuery: () => any;
    };
    updateProfile: {
      useMutation: () => any;
    };
  };
}

export const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error) => {
      toast.error(error.message, {
        action: {
          label: "retry",
          onClick: () => {
            queryClient.invalidateQueries();
          },
        },
      });
    },
  }),
});

export const trpc = createTRPCReact<AppRouter>();

export const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: 'http://localhost:3000/trpc',
      fetch(url, options) {
        return fetch(url, {
          ...options,
          credentials: 'include', // This ensures cookies are sent with cross-origin requests
        });
      },
    }),
  ],
});

